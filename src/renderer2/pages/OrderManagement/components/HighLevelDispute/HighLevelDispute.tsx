import React from 'react';

interface HighLevelDisputeProps {
    children?: React.ReactNode;
    dynamicComponent?: React.ReactNode;
}

// Component that can render dynamic dispute components
const HighLevelDispute: React.FC<HighLevelDisputeProps> = ({ children, dynamicComponent }) => {
    return (
        <div>
            {dynamicComponent && dynamicComponent}
            {children}
        </div>
    );
};

export default HighLevelDispute;