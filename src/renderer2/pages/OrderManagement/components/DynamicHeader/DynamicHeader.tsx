import React from 'react';

interface DynamicHeaderProps {
  /** The React component to render dynamically */
  component?: React.ComponentType<any> | null;
  /** Props to pass to the component */
  componentProps?: Record<string, any>;
}

/**
 * DynamicHeader Component
 * 
 * A flexible header component that can render any React component passed to it.
 * This allows for dynamic header content based on application state.
 * 
 * @example
 * ```tsx
 * // Set a component in the dynamic header
 * setDynamicHeader(BuyerCancellationRequest, { orderId: '123' });
 * 
 * // Clear the dynamic header
 * setDynamicHeader(null);
 * ```
 */
const DynamicHeader: React.FC<DynamicHeaderProps> = ({ 
  component: Component, 
  componentProps = {} 
}) => {
  if (!Component) {
    return null;
  }

  // Use React.createElement to avoid potential hook issues
  return (
    <div className="dynamic-header-container">
      {React.createElement(Component, componentProps)}
    </div>
  );
};

export default DynamicHeader;
